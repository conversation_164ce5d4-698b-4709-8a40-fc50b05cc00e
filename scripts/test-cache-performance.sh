#!/bin/bash

# Script para testar performance do cache após deploy
echo "🧪 Testando performance do cache no Railway..."

# Verificar se está logado
railway whoami || {
    echo "❌ Não autenticado. Execute: railway login"
    exit 1
}

# Função para medir tempo de resposta
test_query() {
    local query="$1"
    local iteration="$2"
    
    echo "🔍 Teste $iteration: '$query'"
    
    # Usar railway run para executar no ambiente Railway
    start_time=$(date +%s%3N)
    
    railway run --service backend python src/main.py "$query" --client-id L2M --sector cambio > /tmp/railway_test_$iteration.log 2>&1
    
    end_time=$(date +%s%3N)
    duration=$((end_time - start_time))
    
    echo "⏱️ Tempo total: ${duration}ms"
    
    # Analisar logs para identificar cache hits
    if grep -q "L1 cache hit" /tmp/railway_test_$iteration.log; then
        echo "⚡ L1 Cache Hit detectado!"
    elif grep -q "L2 cache hit" /tmp/railway_test_$iteration.log; then
        echo "🚀 L2 Cache Hit detectado!"
    elif grep -q "PostgreSQL cache hit" /tmp/railway_test_$iteration.log; then
        echo "💾 L3 Cache Hit detectado!"
    else
        echo "❌ Cache Miss"
    fi
    
    # Verificar se houve resposta
    if grep -q "## Resposta" /tmp/railway_test_$iteration.log; then
        echo "✅ Resposta formatada corretamente"
    else
        echo "⚠️ Problema na formatação da resposta"
    fi
    
    echo "---"
}

# Testes de performance
echo "🚀 Iniciando testes de performance..."
echo "Query de teste: 'quanto vendemos em 2023'"
echo ""

# Primeiro teste (provável cache miss)
test_query "quanto vendemos em 2023" "1"

# Segundo teste (deve ser cache hit)
test_query "quanto vendemos em 2023" "2"

# Terceiro teste (confirmar cache hit)
test_query "quanto vendemos em 2023" "3"

# Variação da query
test_query "vendas totais 2023" "4"

echo "📊 Resumo dos testes salvos em /tmp/railway_test_*.log"
echo ""
echo "🔍 Para ver logs detalhados do Railway:"
echo "railway logs --service backend -f"
echo ""
echo "📈 Para monitorar métricas:"
echo "railway status --service backend"