/**
 * Unified KPIs Hook - SINGLE SOURCE OF TRUTH
 * 
 * Main hook for KPI data management. Consolidates previous:
 * - useKpiData (removed)
 * - useDynamicKpis (merged)  
 * - useKpiDataTransition (merged)
 * 
 * Provides consistent loading states, error handling, and data flow.
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  KpiData,
  DashboardFilters,
  UseKpiDataReturn
} from '@/types/kpi';
import {
  LoadingState,
  ErrorState,
  KpiListResponse,
  DashboardLayoutConfig
} from '@/types/dashboard';
import { UserProfile, ProfileType } from '@/types/profile';

interface UseDynamicKpisProps {
  filters: DashboardFilters;
  autoRefresh?: boolean;
  refreshInterval?: number;
  // Week 5: Profile-aware props (optional for backward compatibility)
  userProfile?: UserProfile | null;
  profileType?: ProfileType;
  enablePersonalization?: boolean;
  userId?: string;
}

interface UseDynamicKpisReturn extends LoadingState, ErrorState {
  kpis: KpiData[];
  layoutConfig: DashboardLayoutConfig | null;
  totalCount: number;
  lastUpdated: string | null;
  refreshKpis: () => Promise<void>;
  togglePriority: (kpiId: string) => void;
  removeKpi: (kpiId: string) => void;
  addKpi: (kpiId: string) => Promise<void>;
  getKpiById: (kpiId: string) => KpiData | undefined;
}

export const useKpis = ({
  filters,
  autoRefresh = false,
  refreshInterval = 30000, // 30 seconds
  // Week 5: Profile-aware props
  userProfile,
  profileType,
  enablePersonalization = false,
  userId
}: UseDynamicKpisProps): UseDynamicKpisReturn => {

  // State management
  const [kpis, setKpis] = useState<KpiData[]>([]);
  const [layoutConfig, setLayoutConfig] = useState<DashboardLayoutConfig | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  
  // Loading states
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isFilterChanging, setIsFilterChanging] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Error state
  const [error, setError] = useState<string | null>(null);

  // Computed loading state
  const isLoading = isInitialLoading || isFilterChanging || isRefreshing;
  const hasError = error !== null;

  // API call to fetch KPIs
  const fetchKpis = useCallback(async (isRefresh = false) => {
    try {
      console.log('🔄 Fetching KPIs with filters:', filters);

      // Set appropriate loading state
      if (isRefresh) {
        setIsRefreshing(true);
      } else if (kpis.length === 0) {
        setIsInitialLoading(true);
      } else {
        setIsFilterChanging(true);
      }

      setError(null);

      // Build query parameters dynamically
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });

      // Add refresh flag if needed
      if (isRefresh) {
        queryParams.append('force_refresh', 'true');
      }

      // Week 5: Add personalization parameters
      if (enablePersonalization && userId) {
        queryParams.append('user_id', userId);

        if (userProfile?.profileType || profileType) {
          queryParams.append('profile_type', userProfile?.profileType || profileType!);
        }
      }

      // Week 5: Use personalized endpoint if personalization is enabled
      if (enablePersonalization && userId) {
        // Use personalized endpoint with POST method
        const requestOptions: RequestInit = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            user_id: userId,
            profile_type: userProfile?.profileType || profileType,
            timeframe: filters.timeframe,
            currency: filters.currency
          })
        };

        const response = await fetch('/api/personalized-kpis', requestOptions);

        if (!response.ok) {
          throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }

        const data: any = await response.json();

        // Handle personalized API response format
        const fetchedKpis = data.kpis || [];

        setKpis(fetchedKpis);
        setTotalCount(data.total_kpis || fetchedKpis.length);
        setLastUpdated(new Date().toISOString());

        console.log(`✅ Loaded ${fetchedKpis.length} personalized KPIs for profile: ${userProfile?.profileType || profileType}`);
        return;
      }

      // Fallback: if personalization is disabled, return empty array
      // (since we don't have a non-personalized endpoint implemented)
      console.warn('⚠️ Personalization disabled, returning empty KPI list');
      setKpis([]);
      setTotalCount(0);
      setLastUpdated(new Date().toISOString());

    } catch (err) {
      console.error('❌ Error fetching KPIs:', err);
      setError(err instanceof Error ? err.message : 'Failed to load KPIs');
      setKpis([]);
    } finally {
      setIsInitialLoading(false);
      setIsFilterChanging(false);
      setIsRefreshing(false);
    }
  }, [filters, enablePersonalization, userId, userProfile, profileType]);

  // Manual refresh function
  const refreshKpis = useCallback(async () => {
    await fetchKpis(true);
  }, [fetchKpis]);

  // Toggle KPI priority
  const togglePriority = useCallback((kpiId: string) => {
    setKpis(prevKpis =>
      prevKpis.map(kpi =>
        kpi.id === kpiId
          ? { 
              ...kpi, 
              is_priority: !kpi.is_priority,
              isPriority: !kpi.is_priority  // Ensure both fields are updated for compatibility
            }
          : kpi
      )
    );

    // TODO: Persist priority change to backend
    console.log(`🔄 Toggled priority for KPI: ${kpiId}`);
  }, []);

  // Remove KPI from dashboard
  const removeKpi = useCallback((kpiId: string) => {
    setKpis(prevKpis => prevKpis.filter(kpi => kpi.id !== kpiId));
    
    // TODO: Persist removal to backend
    console.log(`🗑️ Removed KPI: ${kpiId}`);
  }, []);

  // Add KPI to dashboard
  const addKpi = useCallback(async (kpiId: string) => {
    try {
      // TODO: Call API to add KPI
      console.log(`➕ Adding KPI: ${kpiId}`);
      
      // Refresh to get updated list
      await refreshKpis();
    } catch (err) {
      console.error('❌ Error adding KPI:', err);
      setError(err instanceof Error ? err.message : 'Failed to add KPI');
    }
  }, [refreshKpis]);

  // Get KPI by ID
  const getKpiById = useCallback((kpiId: string): KpiData | undefined => {
    return kpis.find(kpi => kpi.id === kpiId);
  }, [kpis]);

  // Effect: Load KPIs when filters change
  useEffect(() => {
    fetchKpis();
  }, [fetchKpis]);

  // Effect: Auto-refresh if enabled
  useEffect(() => {
    if (!autoRefresh || refreshInterval <= 0) return;

    const interval = setInterval(() => {
      if (!isLoading) {
        refreshKpis();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, isLoading, refreshKpis]);

  // Effect: Log filter changes
  useEffect(() => {
    console.log('🔍 Filters changed:', filters);
  }, [filters]);

  return {
    // Data
    kpis,
    layoutConfig,
    totalCount,
    lastUpdated,

    // Loading states
    isInitialLoading,
    isFilterChanging,
    isRefreshing,
    isLoading,

    // Error state
    error,
    hasError,

    // Actions
    refreshKpis,
    togglePriority,
    removeKpi,
    addKpi,
    getKpiById
  };
};

// Helper functions
export const kpiHelpers = {
  /**
   * Sort KPIs by display order and priority
   */
  sortKpis: (kpis: KpiData[]): KpiData[] => {
    return [...kpis].sort((a, b) => {
      // Priority KPIs first (check both field variants for compatibility)
      const aPriority = a.is_priority || a.isPriority;
      const bPriority = b.is_priority || b.isPriority;
      
      if (aPriority && !bPriority) return -1;
      if (!aPriority && bPriority) return 1;
      
      // Then by display order (check both field variants)
      const aOrder = a.display_order || a.order || 0;
      const bOrder = b.display_order || b.order || 0;
      return aOrder - bOrder;
    });
  },

  /**
   * Filter KPIs by category
   */
  filterByCategory: (kpis: KpiData[], category: string): KpiData[] => {
    return kpis.filter(kpi => kpi.category === category);
  },

  /**
   * Get unique categories from KPIs
   */
  getCategories: (kpis: KpiData[]): string[] => {
    const categories = new Set(kpis.map(kpi => kpi.category));
    return Array.from(categories).sort();
  },

  /**
   * Get priority KPIs only
   */
  getPriorityKpis: (kpis: KpiData[]): KpiData[] => {
    return kpis.filter(kpi => kpi.is_priority);
  }
};

export default useKpis;
