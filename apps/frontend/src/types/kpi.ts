/**
 * KPI Type Definitions - UNIFIED
 * ==============================
 * 
 * Single source of truth for all KPI-related TypeScript definitions.
 * Consolidates previous duplicated definitions across multiple files.
 */

// KPI Trend Types
export type KpiTrend = 'up' | 'down' | 'stable' | 'positive' | 'negative' | 'neutral';

// KPI Format Types  
export type KpiFormat = 'number' | 'currency' | 'percentage';

// KPI Chart Types
export type KpiChartType = 'line' | 'area' | 'bar' | 'gauge';

// KPI Category Types
export type KpiCategory = 'volume' | 'spread' | 'conversion' | 'retention' | 'compliance' | 'performance' | 'general';

// KPI Priority Types
export type KpiPriority = 'critical' | 'high' | 'medium' | 'low';

// Chart Data Point - UNIFIED
export interface ChartDataPoint {
  date?: string;
  name?: string;
  value: number;
  label?: string;
}

// KPI Alert - UNIFIED
export interface KpiAlert {
  id?: string;
  type: 'warning' | 'critical' | 'info' | 'above' | 'below';
  message: string;
  threshold?: number;
  severity?: 'low' | 'medium' | 'high';
  isActive?: boolean;
}

// KPI Definition (metadata only)
export interface KpiDefinition {
  id: string;
  name: string;
  description: string;
  category: KpiCategory;
  priority: KpiPriority;
  unit?: string;
  format_type: KpiFormat;
  chart_type: KpiChartType;
  display_order: number;
  is_priority: boolean;
  frequency: string;
  cache_ttl: number;
  thresholds?: {
    warning?: number;
    critical?: number;
  };
}

// Main KPI Data Interface - UNIFIED
export interface KpiData {
  // Core identification
  id: string;
  title?: string; // For backward compatibility
  name?: string;  // New preferred field
  description: string;

  // Values and metrics
  currentValue: number;
  previousValue?: number;
  changePercent?: number;
  trend: KpiTrend;

  // Formatting and display
  format?: KpiFormat;           // Legacy
  format_type?: KpiFormat;      // New
  unit?: string;
  chartType?: KpiChartType;     // Legacy  
  chart_type?: KpiChartType;    // New
  chartData: ChartDataPoint[];

  // Metadata and categorization
  alert?: KpiAlert;
  isPriority?: boolean;         // Legacy
  is_priority?: boolean;        // New
  order?: number;               // Legacy
  display_order?: number;       // New
  category?: KpiCategory;
  priority?: KpiPriority;
  frequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | string;
  cache_ttl?: number;

  // Timestamps and calculation info
  lastUpdated?: string;
  calculatedAt?: string;
  metadata?: {
    query_time_ms?: number;
    cache_hit?: boolean;
    data_points?: number;
  };
}

// Dashboard Filters
export interface DashboardFilters {
  timeframe: '1d' | 'week' | 'month' | 'quarter';
  currency: 'all' | 'usd' | 'eur' | 'gbp';
  sector?: string;
  client_id?: string;
  priority_only?: boolean;
  category?: KpiCategory;
}

// KPI Calculation Request
export interface KpiCalculationRequest {
  kpi_id: string;
  client_id: string;
  sector: string;
  timeframe: string;
  currency: string;
}

// KPI Metadata
export interface KpiMetadata {
  id: string;
  name: string;
  description: string;
  category: KpiCategory;
  priority: KpiPriority;
  unit: string;
  format_type: KpiFormat;
  chart_type: KpiChartType;
  display_order: number;
  is_priority: boolean;
  frequency: string;
  cache_ttl: number;
  thresholds: {
    warning: number;
    critical: number;
  };
}

// API Response Types
export interface DashboardKpisResponse {
  kpis: KpiData[];
  metadata: {
    client_id: string;
    sector: string;
    timeframe: string;
    currency: string;
    total_kpis: number;
    calculation_time: number;
    cache_hit: boolean;
    last_updated: string;
  };
}

export interface SingleKpiResponse {
  kpi: KpiData;
  metadata: {
    calculation_time: number;
    cache_hit: boolean;
    last_updated: string;
  };
}

// Error Types
export interface ApiErrorResponse {
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
}

// Loading States
export interface KpiLoadingState {
  isLoading: boolean;
  isInitialLoading: boolean;
  isRefreshing: boolean;
  isFilterChanging: boolean;
  error: Error | null;
}

// KPI Actions
export interface KpiActions {
  refresh: () => Promise<void>;
  togglePriority: (kpiId: string) => void;
  updateFilters: (filters: Partial<DashboardFilters>) => void;
  calculateSingle: (kpiId: string) => Promise<KpiData | null>;
}

// Hook Return Type
export interface UseKpiDataReturn extends KpiLoadingState, KpiActions {
  kpis: KpiData[];
  filters: DashboardFilters;
  metadata?: DashboardKpisResponse['metadata'];
}

// Utility Types
export type KpiId = string;
export type ClientId = string;
export type Sector = string;

// Type Guards
export const isKpiData = (obj: any): obj is KpiData => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof obj.id === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.currentValue === 'number' &&
    ['positive', 'negative', 'neutral'].includes(obj.trend) &&
    ['number', 'currency', 'percentage'].includes(obj.format)
  );
};

export const isDashboardFilters = (obj: any): obj is DashboardFilters => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    ['1d', 'week', 'month', 'quarter'].includes(obj.timeframe) &&
    ['all', 'usd', 'eur', 'gbp'].includes(obj.currency)
  );
};

// Default Values
export const DEFAULT_FILTERS: DashboardFilters = {
  timeframe: 'week',
  currency: 'all',
  sector: 'cambio',
  client_id: 'L2M',
  priority_only: true
};

export const DEFAULT_KPI_DATA: Partial<KpiData> = {
  trend: 'neutral',
  format: 'number',
  unit: '',
  chartType: 'line',
  chartData: [],
  isPriority: false,
  order: 999,
  category: 'general',
  frequency: 'daily'
};
