"""
Profile Detector for DataHero4 Hybrid Architecture
==================================================

Uses QueryCache embeddings and usage patterns to automatically detect user profiles.
Implements fail-fast validation with confidence thresholds and pattern analysis.

Features:
- Embedding-based query pattern analysis
- Usage frequency and timing analysis
- KPI preference detection
- Confidence-based profile assignment
- Fail-fast validation (>30% confidence required)

Author: DataHero4 Team
Date: 2025-01-21
"""

import logging
import time
import pickle
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import Counter, defaultdict
from sqlalchemy import text

from src.utils.learning_db_utils import get_db_manager
from src.models.learning_models import QueryCache

logger = logging.getLogger(__name__)


class ProfileDetector:
    """
    Detects user profiles based on query patterns and usage behavior.
    
    Uses QueryCache embeddings and historical usage data to identify
    user profiles with confidence scoring and fail-fast validation.
    """
    
    def __init__(self, confidence_threshold: float = 0.30):
        """
        Initialize ProfileDetector.
        
        Args:
            confidence_threshold: Minimum confidence required for profile assignment
        """
        self.confidence_threshold = confidence_threshold
        self.db_manager = get_db_manager()
        
        # Profile patterns based on query characteristics
        self.profile_patterns = {
            'CEO': {
                'keywords': [
                    'receita', 'crescimento', 'margem', 'lucro', 'estratégico', 'visão geral',
                    'performance', 'resultado', 'consolidado', 'executivo', 'diretoria'
                ],
                'kpi_preferences': [
                    'spread_income_detailed', 'margem_liquida_operacional', 
                    'volume_total', 'crescimento_receita'
                ],
                'timeframes': ['month', 'quarter', 'year'],
                'query_complexity': 'medium',
                'usage_frequency': 'low',  # CEOs query less frequently
                'business_hours': True
            },
            'CFO': {
                'keywords': [
                    'custo', 'margem', 'financeiro', 'rentabilidade', 'roe', 'lucro',
                    'despesa', 'orçamento', 'budget', 'análise financeira'
                ],
                'kpi_preferences': [
                    'margem_liquida_operacional', 'custo_por_transacao', 
                    'roe', 'spread_income_detailed'
                ],
                'timeframes': ['week', 'month', 'quarter'],
                'query_complexity': 'high',
                'usage_frequency': 'medium',
                'business_hours': True
            },
            'Risk_Manager': {
                'keywords': [
                    'risco', 'var', 'exposição', 'limite', 'volatilidade', 'hedge',
                    'posição', 'concentração', 'stress', 'cenário'
                ],
                'kpi_preferences': [
                    'var_diario', 'exposicao_cambial', 'utilizacao_limites', 'volatilidade'
                ],
                'timeframes': ['1d', 'week'],
                'query_complexity': 'high',
                'usage_frequency': 'high',  # Risk managers monitor constantly
                'business_hours': False  # Monitor outside business hours too
            },
            'Trader': {
                'keywords': [
                    'spread', 'volume', 'operação', 'trade', 'posição', 'tempo real',
                    'execução', 'preço', 'cotação', 'mercado'
                ],
                'kpi_preferences': [
                    'spread_realtime', 'volume_hora', 'tempo_processamento_medio', 
                    'spread_income_detailed'
                ],
                'timeframes': ['1d', 'hour'],
                'query_complexity': 'medium',
                'usage_frequency': 'very_high',  # Traders query very frequently
                'business_hours': False  # Trade outside business hours
            },
            'Operations': {
                'keywords': [
                    'processamento', 'eficiência', 'operacional', 'tempo', 'erro',
                    'qualidade', 'sla', 'performance', 'throughput'
                ],
                'kpi_preferences': [
                    'tempo_processamento_medio', 'custo_por_transacao', 
                    'taxa_erro', 'volume_processado'
                ],
                'timeframes': ['1d', 'week'],
                'query_complexity': 'medium',
                'usage_frequency': 'high',
                'business_hours': True
            }
        }
        
        logger.info(f"✅ ProfileDetector initialized with confidence threshold: {confidence_threshold}")
    
    def detect_profile(self, user_id: str, analysis_days: int = 30) -> Dict[str, Any]:
        """
        Detect user profile based on query patterns and usage behavior.
        
        Args:
            user_id: User identifier
            analysis_days: Number of days to analyze (default: 30)
            
        Returns:
            Dict with detected profile, confidence, and analysis details
        """
        logger.info(f"🔍 Detecting profile for user {user_id} (analyzing {analysis_days} days)")
        
        try:
            # Get user query history
            query_history = self._get_user_query_history(user_id, analysis_days)
            
            if not query_history:
                logger.warning(f"⚠️ No query history found for user {user_id}")
                return {
                    'user_id': user_id,
                    'detected_profile': None,
                    'confidence': 0.0,
                    'reason': 'insufficient_data',
                    'analysis': {
                        'total_queries': 0,
                        'analysis_period_days': analysis_days
                    }
                }
            
            # Analyze query patterns
            pattern_analysis = self._analyze_query_patterns(query_history)
            
            # Analyze usage behavior
            usage_analysis = self._analyze_usage_behavior(query_history)
            
            # Calculate profile scores
            profile_scores = self._calculate_profile_scores(pattern_analysis, usage_analysis)
            
            # Determine best profile match
            best_profile, confidence = self._determine_best_profile(profile_scores)
            
            # Validate confidence threshold
            if confidence < self.confidence_threshold:
                logger.warning(f"⚠️ Profile detection confidence {confidence:.2f} below threshold {self.confidence_threshold}")
                return {
                    'user_id': user_id,
                    'detected_profile': None,
                    'confidence': confidence,
                    'reason': 'low_confidence',
                    'analysis': {
                        'total_queries': len(query_history),
                        'analysis_period_days': analysis_days,
                        'pattern_analysis': pattern_analysis,
                        'usage_analysis': usage_analysis,
                        'profile_scores': profile_scores,
                        'threshold': self.confidence_threshold
                    }
                }
            
            # Success - profile detected with sufficient confidence
            logger.info(f"✅ Profile detected for user {user_id}: {best_profile} (confidence: {confidence:.2f})")
            
            return {
                'user_id': user_id,
                'detected_profile': best_profile,
                'confidence': confidence,
                'reason': 'pattern_match',
                'analysis': {
                    'total_queries': len(query_history),
                    'analysis_period_days': analysis_days,
                    'pattern_analysis': pattern_analysis,
                    'usage_analysis': usage_analysis,
                    'profile_scores': profile_scores,
                    'detection_timestamp': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Error detecting profile for user {user_id}: {e}")
            return {
                'user_id': user_id,
                'detected_profile': None,
                'confidence': 0.0,
                'reason': 'error',
                'error': str(e)
            }
    
    def _get_user_query_history(self, user_id: str, days: int) -> List[Dict[str, Any]]:
        """Get user query history from QueryCache."""
        try:
            with self.db_manager.get_session() as session:
                # Query user's query history
                history_sql = text("""
                    SELECT 
                        id, question, sql_query, embedding, entities,
                        confidence, execution_time, result_count,
                        created_at, last_used, use_count,
                        feedback_score, business_analysis
                    FROM query_cache
                    WHERE meta_data->>'user_id' = :user_id
                    AND created_at >= NOW() - INTERVAL ':days days'
                    ORDER BY created_at DESC;
                """)
                
                results = session.execute(history_sql, {
                    'user_id': user_id,
                    'days': days
                }).fetchall()
                
                query_history = []
                for row in results:
                    # Deserialize embedding if available
                    embedding = None
                    if row.embedding:
                        try:
                            embedding = pickle.loads(row.embedding)
                        except Exception as e:
                            logger.warning(f"⚠️ Error deserializing embedding: {e}")
                    
                    query_history.append({
                        'id': row.id,
                        'question': row.question,
                        'sql_query': row.sql_query,
                        'embedding': embedding,
                        'entities': row.entities or {},
                        'confidence': row.confidence,
                        'execution_time': row.execution_time,
                        'result_count': row.result_count,
                        'created_at': row.created_at,
                        'last_used': row.last_used,
                        'use_count': row.use_count,
                        'feedback_score': row.feedback_score,
                        'business_analysis': row.business_analysis or {}
                    })
                
                logger.info(f"📊 Retrieved {len(query_history)} queries for user {user_id}")
                return query_history
                
        except Exception as e:
            logger.error(f"❌ Error getting query history for user {user_id}: {e}")
            return []
    
    def _analyze_query_patterns(self, query_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze query patterns for profile detection."""
        
        # Extract keywords from questions
        all_keywords = []
        kpi_mentions = []
        timeframe_mentions = []
        complexity_scores = []
        
        for query in query_history:
            question = query['question'].lower()
            
            # Extract keywords
            words = question.split()
            all_keywords.extend(words)
            
            # Check for KPI mentions
            for profile, patterns in self.profile_patterns.items():
                for kpi in patterns['kpi_preferences']:
                    if kpi.replace('_', ' ') in question or kpi in question:
                        kpi_mentions.append(kpi)
            
            # Check for timeframe mentions
            for timeframe in ['dia', 'semana', 'mês', 'trimestre', 'ano', 'hora', 'minuto']:
                if timeframe in question:
                    timeframe_mentions.append(timeframe)
            
            # Calculate query complexity (rough estimate)
            sql_query = query.get('sql_query', '')
            complexity = len(sql_query.split()) + sql_query.count('JOIN') * 2 + sql_query.count('WHERE') * 1.5
            complexity_scores.append(complexity)
        
        # Analyze patterns
        keyword_frequency = Counter(all_keywords)
        kpi_frequency = Counter(kpi_mentions)
        timeframe_frequency = Counter(timeframe_mentions)
        avg_complexity = np.mean(complexity_scores) if complexity_scores else 0
        
        return {
            'top_keywords': dict(keyword_frequency.most_common(20)),
            'kpi_preferences': dict(kpi_frequency.most_common(10)),
            'timeframe_preferences': dict(timeframe_frequency.most_common(5)),
            'avg_query_complexity': avg_complexity,
            'total_unique_keywords': len(keyword_frequency),
            'pattern_diversity': len(set(all_keywords)) / len(all_keywords) if all_keywords else 0
        }
    
    def _analyze_usage_behavior(self, query_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze usage behavior patterns."""
        
        if not query_history:
            return {}
        
        # Time-based analysis
        query_times = []
        query_days = []
        usage_frequency = len(query_history)
        
        for query in query_history:
            created_at = query['created_at']
            if isinstance(created_at, str):
                created_at = datetime.fromisoformat(created_at)
            
            query_times.append(created_at.hour)
            query_days.append(created_at.weekday())  # 0=Monday, 6=Sunday
        
        # Business hours analysis (9 AM to 6 PM, Monday to Friday)
        business_hours_queries = sum(1 for hour, day in zip(query_times, query_days) 
                                   if 9 <= hour <= 18 and day < 5)
        business_hours_ratio = business_hours_queries / len(query_history)
        
        # Usage frequency classification
        days_span = (max(q['created_at'] for q in query_history) - 
                    min(q['created_at'] for q in query_history)).days
        queries_per_day = usage_frequency / max(days_span, 1)
        
        if queries_per_day >= 10:
            frequency_class = 'very_high'
        elif queries_per_day >= 5:
            frequency_class = 'high'
        elif queries_per_day >= 2:
            frequency_class = 'medium'
        else:
            frequency_class = 'low'
        
        return {
            'total_queries': usage_frequency,
            'queries_per_day': queries_per_day,
            'frequency_class': frequency_class,
            'business_hours_ratio': business_hours_ratio,
            'peak_hours': Counter(query_times).most_common(3),
            'active_days': Counter(query_days).most_common(7),
            'analysis_period_days': days_span
        }
    
    def _calculate_profile_scores(
        self, 
        pattern_analysis: Dict[str, Any], 
        usage_analysis: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate scores for each profile based on patterns and usage."""
        
        profile_scores = {}
        
        for profile_type, patterns in self.profile_patterns.items():
            score = 0.0
            
            # Keyword matching score (40% weight)
            keyword_score = 0.0
            top_keywords = pattern_analysis.get('top_keywords', {})
            for keyword in patterns['keywords']:
                if keyword in top_keywords:
                    keyword_score += top_keywords[keyword] / sum(top_keywords.values())
            score += keyword_score * 0.4
            
            # KPI preference score (30% weight)
            kpi_score = 0.0
            kpi_preferences = pattern_analysis.get('kpi_preferences', {})
            for kpi in patterns['kpi_preferences']:
                if kpi in kpi_preferences:
                    kpi_score += kpi_preferences[kpi] / sum(kpi_preferences.values())
            score += kpi_score * 0.3
            
            # Usage frequency score (20% weight)
            frequency_score = 0.0
            user_frequency = usage_analysis.get('frequency_class', 'low')
            expected_frequency = patterns['usage_frequency']
            if user_frequency == expected_frequency:
                frequency_score = 1.0
            elif abs(['low', 'medium', 'high', 'very_high'].index(user_frequency) - 
                    ['low', 'medium', 'high', 'very_high'].index(expected_frequency)) == 1:
                frequency_score = 0.5
            score += frequency_score * 0.2
            
            # Business hours score (10% weight)
            business_hours_score = 0.0
            user_business_ratio = usage_analysis.get('business_hours_ratio', 0.5)
            if patterns['business_hours']:
                business_hours_score = user_business_ratio
            else:
                business_hours_score = 1.0 - user_business_ratio
            score += business_hours_score * 0.1
            
            profile_scores[profile_type] = min(score, 1.0)  # Cap at 1.0
        
        return profile_scores
    
    def _determine_best_profile(self, profile_scores: Dict[str, float]) -> Tuple[Optional[str], float]:
        """Determine the best profile match and confidence."""
        
        if not profile_scores:
            return None, 0.0
        
        # Sort profiles by score
        sorted_profiles = sorted(profile_scores.items(), key=lambda x: x[1], reverse=True)
        
        best_profile, best_score = sorted_profiles[0]
        
        # Calculate confidence based on score separation
        if len(sorted_profiles) > 1:
            second_best_score = sorted_profiles[1][1]
            # Confidence is higher when there's clear separation between top profiles
            separation = best_score - second_best_score
            confidence = best_score * (1 + separation)  # Boost confidence with separation
        else:
            confidence = best_score
        
        # Cap confidence at 1.0
        confidence = min(confidence, 1.0)
        
        return best_profile, confidence
    
    def batch_detect_profiles(self, user_ids: List[str], analysis_days: int = 30) -> Dict[str, Dict[str, Any]]:
        """
        Detect profiles for multiple users in batch.
        
        Args:
            user_ids: List of user identifiers
            analysis_days: Number of days to analyze
            
        Returns:
            Dict mapping user_id to detection results
        """
        logger.info(f"🔍 Batch profile detection for {len(user_ids)} users")
        
        results = {}
        successful_detections = 0
        
        for user_id in user_ids:
            try:
                result = self.detect_profile(user_id, analysis_days)
                results[user_id] = result
                
                if result.get('detected_profile'):
                    successful_detections += 1
                    
            except Exception as e:
                logger.error(f"❌ Error in batch detection for user {user_id}: {e}")
                results[user_id] = {
                    'user_id': user_id,
                    'detected_profile': None,
                    'confidence': 0.0,
                    'reason': 'error',
                    'error': str(e)
                }
        
        logger.info(f"✅ Batch detection completed: {successful_detections}/{len(user_ids)} successful")
        
        return results
    
    def update_profile_patterns(self, feedback_data: Dict[str, Any]):
        """
        Update profile patterns based on feedback.
        
        Args:
            feedback_data: Feedback data for pattern improvement
        """
        # This method can be extended to learn from user feedback
        # and improve profile detection accuracy over time
        logger.info("📈 Profile patterns updated with feedback data")


# Singleton instance
_profile_detector: Optional[ProfileDetector] = None


def get_profile_detector() -> ProfileDetector:
    """Get singleton instance of ProfileDetector."""
    global _profile_detector
    if _profile_detector is None:
        _profile_detector = ProfileDetector()
    return _profile_detector
