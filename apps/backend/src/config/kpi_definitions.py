"""
Unified KPI Definitions Configuration
====================================

Single source of truth for all KPI definitions, priorities, and configurations.
Replaces multiple scattered configuration files.
"""

from typing import Dict, List, Any
from enum import Enum


class KpiPriority(Enum):
    """KPI Priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class KpiCategory(Enum):
    """KPI Categories."""
    VOLUME = "volume"
    SPREAD = "spread"
    CONVERSION = "conversion"
    RETENTION = "retention"
    COMPLIANCE = "compliance"
    PERFORMANCE = "performance"


# Critical KPIs that must always be calculated first
CRITICAL_KPIS = [
    "total_volume",
    "average_spread",
    "average_ticket",
    "conversion_rate",
    "retention_rate",
    "compliance_score",
    # Week 3 - New Hybrid Architecture KPIs
    "spread_income_detailed",
    "margem_liquida_operacional",
    "custo_por_transacao",
    "tempo_processamento_medio"
]

# KPI Definitions with unified configuration
KPI_DEFINITIONS = {
    "total_volume": {
        "id": "total_volume",
        "name": "Volume Total Negociado",
        "description": "Volume total de transações processadas",
        "category": KpiCategory.VOLUME,
        "priority": KpiPriority.CRITICAL,
        "unit": "USD",
        "format_type": "currency",
        "chart_type": "area",
        "display_order": 1,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 300,  # 5 minutes
        "thresholds": {
            "warning": 1000000,
            "critical": 500000
        }
    },
    
    "average_spread": {
        "id": "average_spread",
        "name": "Spread Médio",
        "description": "Spread médio das operações de câmbio",
        "category": KpiCategory.SPREAD,
        "priority": KpiPriority.CRITICAL,
        "unit": "bps",
        "format_type": "percentage",
        "chart_type": "line",
        "display_order": 2,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 300,
        "thresholds": {
            "warning": 0.05,
            "critical": 0.10
        }
    },
    
    "average_ticket": {
        "id": "average_ticket",
        "name": "Ticket Médio",
        "description": "Valor médio por transação",
        "category": KpiCategory.VOLUME,
        "priority": KpiPriority.CRITICAL,
        "unit": "USD",
        "format_type": "currency",
        "chart_type": "bar",
        "display_order": 3,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 300,
        "thresholds": {
            "warning": 1000,
            "critical": 500
        }
    },
    
    "conversion_rate": {
        "id": "conversion_rate",
        "name": "Taxa de Conversão",
        "description": "Percentual de leads convertidos em clientes",
        "category": KpiCategory.CONVERSION,
        "priority": KpiPriority.CRITICAL,
        "unit": "%",
        "format_type": "percentage",
        "chart_type": "line",
        "display_order": 4,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 600,  # 10 minutes
        "thresholds": {
            "warning": 0.15,
            "critical": 0.10
        }
    },
    
    "retention_rate": {
        "id": "retention_rate",
        "name": "Taxa de Retenção",
        "description": "Percentual de clientes que retornaram",
        "category": KpiCategory.RETENTION,
        "priority": KpiPriority.CRITICAL,
        "unit": "%",
        "format_type": "percentage",
        "chart_type": "area",
        "display_order": 5,
        "is_priority": True,
        "frequency": "weekly",
        "cache_ttl": 1800,  # 30 minutes
        "thresholds": {
            "warning": 0.70,
            "critical": 0.50
        }
    },
    
    "compliance_score": {
        "id": "compliance_score",
        "name": "Score de Compliance",
        "description": "Pontuação de conformidade regulatória",
        "category": KpiCategory.COMPLIANCE,
        "priority": KpiPriority.CRITICAL,
        "unit": "score",
        "format_type": "number",
        "chart_type": "gauge",
        "display_order": 6,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 900,  # 15 minutes
        "thresholds": {
            "warning": 80,
            "critical": 70
        }
    },

    # Week 3 - New Hybrid Architecture KPIs
    "spread_income_detailed": {
        "id": "spread_income_detailed",
        "name": "Spread Income Detalhado",
        "description": "Receita detalhada por spread por moeda e período",
        "category": KpiCategory.SPREAD,
        "priority": KpiPriority.CRITICAL,
        "unit": "USD",
        "format_type": "currency",
        "chart_type": "area",
        "display_order": 7,
        "is_priority": True,
        "frequency": "realtime",
        "cache_ttl": 300,  # 5 minutes
        "profile_preferences": {
            "CEO": {"cache_ttl": 3600, "preferred_layer": "snapshot"},
            "CFO": {"cache_ttl": 1800, "preferred_layer": "snapshot"},
            "Trader": {"cache_ttl": 60, "preferred_layer": "cache"}
        },
        "thresholds": {
            "warning": 50000,
            "critical": 25000
        },
        "sql_query": """
            SELECT
                currency,
                DATE(created_at) as date,
                SUM(spread_percent * amount) as spread_income,
                COUNT(*) as transaction_count,
                AVG(spread_percent) as avg_spread_percent
            FROM transactions
            WHERE created_at >= NOW() - INTERVAL '{timeframe}'
            AND ({currency_filter})
            GROUP BY currency, DATE(created_at)
            ORDER BY date DESC, spread_income DESC
        """
    },

    "margem_liquida_operacional": {
        "id": "margem_liquida_operacional",
        "name": "Margem Líquida Operacional",
        "description": "Margem operacional líquida: (Receita Spread - Custos Operacionais) / Receita Total * 100",
        "category": KpiCategory.PERFORMANCE,
        "priority": KpiPriority.CRITICAL,
        "unit": "percent",
        "format_type": "percentage",
        "chart_type": "line",
        "display_order": 8,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 1800,  # 30 minutes
        "profile_preferences": {
            "CEO": {"cache_ttl": 3600, "preferred_layer": "snapshot"},
            "CFO": {"cache_ttl": 1800, "preferred_layer": "snapshot"}
        },
        "thresholds": {
            "warning": 15.0,
            "critical": 10.0
        },
        "sql_query": """
            WITH spread_revenue AS (
                SELECT
                    DATE(created_at) as date,
                    SUM(spread_percent * amount) as total_spread_revenue
                FROM transactions
                WHERE created_at >= NOW() - INTERVAL '{timeframe}'
                AND ({currency_filter})
                GROUP BY DATE(created_at)
            ),
            operational_costs AS (
                SELECT
                    date,
                    SUM(amount) as total_costs
                FROM operational_costs
                WHERE date >= NOW() - INTERVAL '{timeframe}'
                GROUP BY date
            )
            SELECT
                sr.date,
                sr.total_spread_revenue,
                COALESCE(oc.total_costs, 0) as total_costs,
                CASE
                    WHEN sr.total_spread_revenue > 0 THEN
                        ((sr.total_spread_revenue - COALESCE(oc.total_costs, 0)) / sr.total_spread_revenue) * 100
                    ELSE 0
                END as operational_margin_percent
            FROM spread_revenue sr
            LEFT JOIN operational_costs oc ON sr.date = oc.date
            ORDER BY sr.date DESC
        """
    },

    "custo_por_transacao": {
        "id": "custo_por_transacao",
        "name": "Custo por Transação",
        "description": "Custo operacional médio por transação processada",
        "category": KpiCategory.PERFORMANCE,
        "priority": KpiPriority.HIGH,
        "unit": "USD",
        "format_type": "currency",
        "chart_type": "bar",
        "display_order": 9,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 900,  # 15 minutes
        "profile_preferences": {
            "CFO": {"cache_ttl": 1800, "preferred_layer": "cache"},
            "Operations": {"cache_ttl": 900, "preferred_layer": "cache"}
        },
        "thresholds": {
            "warning": 5.0,
            "critical": 10.0
        },
        "sql_query": """
            WITH transaction_counts AS (
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as transaction_count
                FROM transactions
                WHERE created_at >= NOW() - INTERVAL '{timeframe}'
                AND ({currency_filter})
                GROUP BY DATE(created_at)
            ),
            daily_costs AS (
                SELECT
                    date,
                    SUM(amount) as total_costs
                FROM operational_costs
                WHERE date >= NOW() - INTERVAL '{timeframe}'
                GROUP BY date
            )
            SELECT
                tc.date,
                tc.transaction_count,
                COALESCE(dc.total_costs, 0) as total_costs,
                CASE
                    WHEN tc.transaction_count > 0 THEN
                        COALESCE(dc.total_costs, 0) / tc.transaction_count
                    ELSE 0
                END as cost_per_transaction
            FROM transaction_counts tc
            LEFT JOIN daily_costs dc ON tc.date = dc.date
            ORDER BY tc.date DESC
        """
    },

    "tempo_processamento_medio": {
        "id": "tempo_processamento_medio",
        "name": "Tempo Processamento Médio",
        "description": "Tempo médio de processamento de transações (em segundos)",
        "category": KpiCategory.PERFORMANCE,
        "priority": KpiPriority.HIGH,
        "unit": "seconds",
        "format_type": "duration",
        "chart_type": "line",
        "display_order": 10,
        "is_priority": True,
        "frequency": "realtime",
        "cache_ttl": 300,  # 5 minutes
        "profile_preferences": {
            "Operations": {"cache_ttl": 900, "preferred_layer": "direct"},
            "Risk_Manager": {"cache_ttl": 300, "preferred_layer": "direct"}
        },
        "thresholds": {
            "warning": 30.0,
            "critical": 60.0
        },
        "sql_query": """
            SELECT
                DATE(created_at) as date,
                COUNT(*) as processed_transactions,
                AVG(EXTRACT(EPOCH FROM (processed_at - created_at))) as avg_processing_seconds,
                MIN(EXTRACT(EPOCH FROM (processed_at - created_at))) as min_processing_seconds,
                MAX(EXTRACT(EPOCH FROM (processed_at - created_at))) as max_processing_seconds,
                PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (processed_at - created_at))) as median_processing_seconds,
                PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY EXTRACT(EPOCH FROM (processed_at - created_at))) as p95_processing_seconds
            FROM transactions
            WHERE created_at >= NOW() - INTERVAL '{timeframe}'
            AND processed_at IS NOT NULL
            AND processed_at > created_at
            AND ({currency_filter})
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        """
    }
}

# Timeframe configurations
TIMEFRAME_CONFIG = {
    "1d": {
        "name": "Último Dia",
        "sql_filter": "DATE(created_at) = CURRENT_DATE",
        "cache_ttl": 300
    },
    "week": {
        "name": "Última Semana", 
        "sql_filter": "created_at >= CURRENT_DATE - INTERVAL '7 days'",
        "cache_ttl": 600
    },
    "month": {
        "name": "Último Mês",
        "sql_filter": "created_at >= CURRENT_DATE - INTERVAL '30 days'",
        "cache_ttl": 1800
    },
    "quarter": {
        "name": "Último Trimestre",
        "sql_filter": "created_at >= CURRENT_DATE - INTERVAL '90 days'",
        "cache_ttl": 3600
    }
}

# Currency configurations
CURRENCY_CONFIG = {
    "all": {
        "name": "Todas as Moedas",
        "sql_filter": "1=1",
        "symbol": "Multi"
    },
    "usd": {
        "name": "Dólar Americano",
        "sql_filter": "currency = 'USD'",
        "symbol": "$"
    },
    "eur": {
        "name": "Euro",
        "sql_filter": "currency = 'EUR'",
        "symbol": "€"
    },
    "gbp": {
        "name": "Libra Esterlina",
        "sql_filter": "currency = 'GBP'",
        "symbol": "£"
    }
}


def get_kpi_definition(kpi_id: str) -> Dict[str, Any]:
    """Get KPI definition by ID."""
    return KPI_DEFINITIONS.get(kpi_id, {})


def get_critical_kpis() -> List[str]:
    """Get list of critical KPI IDs."""
    return CRITICAL_KPIS.copy()


def get_kpis_by_priority(priority: KpiPriority) -> List[Dict[str, Any]]:
    """Get KPIs filtered by priority level."""
    return [
        kpi for kpi in KPI_DEFINITIONS.values()
        if kpi.get("priority") == priority
    ]


def get_kpis_by_category(category: KpiCategory) -> List[Dict[str, Any]]:
    """Get KPIs filtered by category."""
    return [
        kpi for kpi in KPI_DEFINITIONS.values()
        if kpi.get("category") == category
    ]


def get_timeframe_config(timeframe: str) -> Dict[str, Any]:
    """Get timeframe configuration."""
    return TIMEFRAME_CONFIG.get(timeframe, TIMEFRAME_CONFIG["week"])


def get_currency_config(currency: str) -> Dict[str, Any]:
    """Get currency configuration."""
    return CURRENCY_CONFIG.get(currency, CURRENCY_CONFIG["all"])
