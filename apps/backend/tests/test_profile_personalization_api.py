"""
Profile Personalization API Tests
=================================

Tests for profile-based KPI personalization API endpoints.
Validates that each profile returns the correct KPIs with real data.
"""

import pytest
import requests
import json
from typing import Dict, Any

# Test configuration
BASE_URL = "http://localhost:8000"
API_ENDPOINT = f"{BASE_URL}/api/personalized-kpis"

# Expected KPIs per profile
EXPECTED_KPIS = {
    "CEO": ["spread_income_detailed", "margem_liquida_operacional"],
    "CFO": ["margem_liquida_operacional"],
    "Trader": ["spread_income_detailed"],
    "Operations": ["custo_por_transacao", "tempo_processamento_medio"],
    "Risk_Manager": ["tempo_processamento_medio"]
}

def test_api_server_running():
    """Test that the API server is running and accessible."""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        assert response.status_code in [200, 404], "API server should be running"
    except requests.exceptions.RequestException:
        pytest.skip("API server not running - start with 'poetry run uvicorn src.interfaces.api:app'")

@pytest.mark.parametrize("profile_type,expected_kpis", [
    ("CEO", EXPECTED_KPIS["CEO"]),
    ("CFO", EXPECTED_KPIS["CFO"]),
    ("Trader", EXPECTED_KPIS["Trader"]),
    ("Operations", EXPECTED_KPIS["Operations"]),
    ("Risk_Manager", EXPECTED_KPIS["Risk_Manager"])
])
def test_profile_personalization(profile_type: str, expected_kpis: list):
    """Test that each profile returns the correct KPIs."""
    
    # Prepare request payload
    payload = {
        "user_id": f"test_{profile_type.lower()}",
        "profile_type": profile_type,
        "timeframe": "week",
        "currency": "all",
        "priority_only": profile_type in ["CEO", "CFO", "Trader"]  # Priority filter for these profiles
    }
    
    # Make API request
    response = requests.post(API_ENDPOINT, json=payload, timeout=30)
    
    # Validate response
    assert response.status_code == 200, f"API should return 200 for {profile_type}"
    
    data = response.json()
    
    # Validate response structure
    assert "kpis" in data, "Response should contain 'kpis' field"
    assert "personalization" in data, "Response should contain 'personalization' field"
    
    # Validate personalization
    personalization = data["personalization"]
    assert "recommended_kpis" in personalization, "Should contain recommended KPIs"

    # Check if profile_type is in personalization, if not that's ok
    if "profile_type" in personalization:
        assert personalization["profile_type"] == profile_type, f"Profile type should be {profile_type}"
    
    # Validate KPI count
    kpis = data["kpis"]
    expected_count = len(expected_kpis)
    actual_count = len(kpis)
    
    assert actual_count == expected_count, f"{profile_type} should return {expected_count} KPIs, got {actual_count}"
    
    # Validate KPI IDs
    actual_kpi_ids = [kpi["id"] for kpi in kpis]
    for expected_kpi in expected_kpis:
        assert expected_kpi in actual_kpi_ids, f"{profile_type} should include KPI {expected_kpi}"

    # Validate KPI data quality
    for kpi in kpis:
        assert "id" in kpi, "Each KPI should have an ID"
        assert "currentValue" in kpi, "Each KPI should have a current value"
        assert "title" in kpi, "Each KPI should have a title"
        assert "source" in kpi, "Each KPI should have a source"

        # Validate real data (not mock)
        assert kpi["source"] in ["hybrid_calculation", "hybrid_direct"], "Should use real calculation"
        assert "metadata" in kpi, "Should include metadata"

        metadata = kpi["metadata"]
        # Check for real schema indicators
        has_real_schema = any(key in str(metadata) for key in ["real_schema", "boleta", "total_transactions"])
        assert has_real_schema, f"Should use real schema, got metadata: {metadata}"

def test_ceo_profile_comprehensive():
    """Comprehensive test for CEO profile (most KPIs)."""
    
    payload = {
        "user_id": "test_ceo_comprehensive",
        "profile_type": "CEO",
        "timeframe": "week",
        "currency": "all",
        "priority_only": True
    }
    
    response = requests.post(API_ENDPOINT, json=payload, timeout=30)
    assert response.status_code == 200
    
    data = response.json()
    kpis = data["kpis"]
    
    # CEO should have 2 KPIs
    assert len(kpis) == 2, "CEO should have exactly 2 KPIs"
    
    # Find specific KPIs
    spread_income_kpi = next((kpi for kpi in kpis if kpi["id"] == "spread_income_detailed"), None)
    margin_kpi = next((kpi for kpi in kpis if kpi["id"] == "margem_liquida_operacional"), None)
    
    assert spread_income_kpi is not None, "CEO should have spread income KPI"
    assert margin_kpi is not None, "CEO should have operational margin KPI"
    
    # Validate spread income KPI
    assert spread_income_kpi["currentValue"] is not None, "Spread income should have a value"
    assert "metadata" in spread_income_kpi, "Should have metadata"
    assert "total_transactions" in spread_income_kpi["metadata"], "Should include transaction count"
    
    # Validate margin KPI
    assert margin_kpi["currentValue"] is not None, "Margin should have a value"
    # Check if formattedValue exists, if not check format field
    if "formattedValue" in margin_kpi:
        assert "%" in margin_kpi["formattedValue"], "Margin should be formatted as percentage"
    elif "format" in margin_kpi:
        assert margin_kpi["format"] == "percentage", "Margin should have percentage format"

def test_operations_profile_comprehensive():
    """Comprehensive test for Operations profile (different KPIs)."""
    
    payload = {
        "user_id": "test_operations_comprehensive", 
        "profile_type": "Operations",
        "timeframe": "week",
        "currency": "all",
        "priority_only": False  # Operations needs priority_only=false
    }
    
    response = requests.post(API_ENDPOINT, json=payload, timeout=30)
    assert response.status_code == 200
    
    data = response.json()
    kpis = data["kpis"]
    
    # Operations should have 2 KPIs
    assert len(kpis) == 2, "Operations should have exactly 2 KPIs"
    
    # Find specific KPIs
    cost_kpi = next((kpi for kpi in kpis if kpi["id"] == "custo_por_transacao"), None)
    time_kpi = next((kpi for kpi in kpis if kpi["id"] == "tempo_processamento_medio"), None)
    
    assert cost_kpi is not None, "Operations should have cost per transaction KPI"
    assert time_kpi is not None, "Operations should have processing time KPI"
    
    # Validate cost KPI
    assert cost_kpi["currentValue"] is not None, "Cost should have a value"
    assert cost_kpi["format"] == "currency", "Cost should have currency format"

    # Validate time KPI
    assert time_kpi["currentValue"] is not None, "Time should have a value"
    # Time KPI should have some format indication
    assert "format" in time_kpi or "unit" in time_kpi, "Time should have format or unit"

if __name__ == "__main__":
    # Run tests manually
    print("🧪 Running Profile Personalization API Tests...")
    
    try:
        test_api_server_running()
        print("✅ API server is running")
        
        for profile_type, expected_kpis in EXPECTED_KPIS.items():
            test_profile_personalization(profile_type, expected_kpis)
            print(f"✅ {profile_type} profile test passed")
        
        test_ceo_profile_comprehensive()
        print("✅ CEO comprehensive test passed")
        
        test_operations_profile_comprehensive()
        print("✅ Operations comprehensive test passed")
        
        print("\n🎉 All tests passed! Profile personalization is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
