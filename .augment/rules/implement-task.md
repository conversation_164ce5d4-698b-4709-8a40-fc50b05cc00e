---
type: "manual"
---

quando o usuário mandar implementar alguma task ou grupo de tasks:

1. se prepare para implementar antes pesquisando primeiro:
- o codebase, 
- os documentos em @/Users/<USER>/Coding/datahero4/docs/ 
- com context7 e perplexity, a documentação atual das dependências críticas 
e exemplos de sucesso de implementação (incluindo github com muitas estrelas). 

2. em seguida, comece a implementação gradual, marcando as tarefas já concluídas.  

EM HIPÓTESE ALGUMA USE MOCK OU FALLBACK. FAIL FAST AND LOUD, se preciso.

Quando algo não funcionar, não crie versões simples apenas para marcar como cumprida a tarefa:
- Recomece o processo para entender a causa raiz e definir o problema, pensar em 3 soluções, e escolher a melhor para tentar de novo 